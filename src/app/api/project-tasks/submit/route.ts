

import { NextRequest, NextResponse } from 'next/server';
import path from 'path';
import { readFile } from 'fs/promises';
import { eventLogger, NOTIFICATION_TYPES, ENTITY_TYPES } from '../../../../lib/events/event-logger';
import { getProjectById } from '../../../../app/api/payments/repos/projects-repo';
import { getTaskById, updateTask } from '../../../../app/api/payments/repos/tasks-repo';
import { requireSession, assert, assertProjectAccess } from '../../../../lib/auth/session-guard';
import { ok, err, ErrorCodes, withErrorHandling } from '../../../../lib/http/envelope';
import { logTaskTransition, Subsystems } from '../../../../lib/log/transitions';

const usersFilePath = path.join(process.cwd(), 'data/users.json');

async function handleTaskSubmission(request: NextRequest) {
  try {
    // 🔒 Auth - get session and validate
    const { userId: actorId } = await requireSession(request);

    const { projectId, taskId, action, referenceUrl } = await request.json();
    assert(projectId && taskId && action, ErrorCodes.MISSING_REQUIRED_FIELD, 400, 'Missing required fields: projectId, taskId, action');

    // Load project first to validate access
    const projectInfo = await getProjectById(Number(projectId));
    assert(projectInfo, ErrorCodes.PROJECT_NOT_FOUND, 404, 'Project not found');

    // Load task to validate it exists and belongs to project
    const task = await getTaskById(Number(taskId));
    assert(task, ErrorCodes.TASK_NOT_FOUND, 404, 'Task not found');
    assert(Number(task!.projectId) === Number(projectId), ErrorCodes.INVALID_INPUT, 400, 'Task does not belong to provided project');

    // 🔒 Validate user permissions based on action
    if (action === 'submit' || action === 'resubmit') {
      // Only freelancer can submit tasks
      assertProjectAccess(actorId, projectInfo!, 'freelancer');
    } else if (action === 'reject') {
      // Only commissioner can reject tasks
      assertProjectAccess(actorId, projectInfo!, 'commissioner');
    } else {
      throw Object.assign(new Error('Unknown action'), { code: ErrorCodes.INVALID_INPUT, status: 400 });
    }

    // Load user data for notifications
    const usersData = await readFile(usersFilePath, 'utf-8');
    const users = JSON.parse(usersData);

    let eventType: string | null = null;
    let targetUserId: number | null = null;

    // Create updated task object
    const updatedTask = { ...task };

    switch (action) {
      case 'submit':
        // First submission: mark as in review but NOT completed (completed = true only when approved)
        updatedTask.completed = false; // Keep false until approved by commissioner
        updatedTask.status = 'In review';
        updatedTask.submittedDate = new Date().toISOString();
        // Update task link with submitted reference URL
        if (referenceUrl) {
          updatedTask.link = referenceUrl;
        }
        // Version stays the same (1) for first submission
        if (!updatedTask.version) updatedTask.version = 1;
        eventType = 'task_submitted';
        targetUserId = projectInfo!.commissionerId;
        break;
      case 'resubmit':
        // Resubmission after rejection: increment version and mark as in review but NOT completed
        updatedTask.rejected = false;
        updatedTask.completed = false; // Keep false until approved by commissioner
        updatedTask.status = 'In review';
        updatedTask.submittedDate = new Date().toISOString();
        // Update task link with submitted reference URL
        if (referenceUrl) {
          updatedTask.link = referenceUrl;
        }
        updatedTask.version = (updatedTask.version || 1) + 1;
        eventType = 'task_submitted';
        targetUserId = projectInfo!.commissionerId;
        break;
      case 'complete':
        // Use transaction service for atomic task approval
        try {
          const { executeTaskApprovalTransaction } = await import('../../../lib/transactions/transaction-service');

          const transactionParams = {
            taskId: Number(taskId),
            projectId: Number(projectId),
            freelancerId: actualFreelancerId,
            commissionerId: actualCommissionerId,
            taskTitle: task.title,
            projectTitle: projectInfo?.title || `Project ${projectId}`,
            generateInvoice: projectInfo?.invoicingMethod === 'completion',
            invoiceType: 'completion' as const
          };

          const transactionResult = await executeTaskApprovalTransaction(transactionParams);

          if (!transactionResult.success) {
            return NextResponse.json({
              success: false,
              error: 'Task approval transaction failed',
              details: transactionResult.error,
              transactionId: transactionResult.transactionId
            }, { status: 500 });
          }

          console.log(`✅ Task approval transaction completed: ${transactionResult.transactionId}`);

          // Return success with transaction details
          return NextResponse.json({
            success: true,
            message: 'Task approved successfully with transaction integrity',
            transaction: {
              id: transactionResult.transactionId,
              completedSteps: transactionResult.completedSteps,
              results: transactionResult.results
            }
          });

        } catch (transactionError) {
          console.error('Transaction service error:', transactionError);
          // Fallback to original logic if transaction service fails
          updatedTask.completed = true;
          updatedTask.status = 'Approved';
          updatedTask.rejected = false;
          updatedTask.approvedDate = new Date().toISOString();
          eventType = 'task_approved';
          targetUserId = projectInfo!.freelancerId;
        }
        break;
      case 'reject':
        // Commissioner rejects the task - freelancer needs to work on it again
        updatedTask.rejected = true;
        updatedTask.completed = false;
        updatedTask.status = 'Ongoing'; // Back to ongoing so freelancer can work on it
        updatedTask.rejectedDate = new Date().toISOString();
        updatedTask.feedbackCount = (updatedTask.feedbackCount || 0) + 1;
        eventType = 'task_rejected';
        targetUserId = projectInfo!.freelancerId;
        break;
      default:
        return NextResponse.json({ error: 'Unknown action' }, { status: 400 });
    }

    // Update task using repository (skip if transaction service was used)
    if (action !== 'complete' || !updatedTask.approvedDate) {
      const updateSuccess = await updateTask(Number(taskId), updatedTask);
      assert(updateSuccess, ErrorCodes.INTERNAL_ERROR, 500, 'Failed to update task');

      // Also update hierarchical storage with project creation date for consistency
      try {
        const { writeTask } = await import('../../../lib/project-tasks/hierarchical-storage');
        await writeTask(updatedTask as any, projectInfo?.createdAt);
      } catch (hierarchicalError) {
        console.warn('Failed to update hierarchical storage:', hierarchicalError);
        // Don't fail the main operation if hierarchical update fails
      }
    }

    // Log the transition for observability
    logTaskTransition(
      Number(taskId),
      task!.status,
      updatedTask.status,
      actorId,
      Subsystems.TASKS_SUBMIT,
      {
        projectId: Number(projectId),
        action: action,
        version: updatedTask.version,
      }
    );

    // Log event for notification system
    if (eventType && targetUserId) {
      try {
        // Validate commissioner ID for task submissions
        if (eventType === 'task_submitted' && !projectInfo!.commissionerId) {
          console.error('❌ Cannot send task submission notification: Commissioner ID is missing');
          console.error('Project info:', { projectId, commissionerId: projectInfo?.commissionerId });
          throw new Error('Commissioner ID is required for task submission notifications');
        }

        // Get freelancer and commissioner names for notification
        const freelancer = users.find((u: any) => u.id === projectInfo!.freelancerId);
        const commissioner = users.find((u: any) => u.id === projectInfo!.commissionerId);

        await eventLogger.logEvent({
          id: `${eventType}_${taskId}_${Date.now()}`,
          timestamp: new Date().toISOString(),
          type: eventType as any,
          notificationType: eventType === 'task_submitted' ? 10 : eventType === 'task_approved' ? 11 : 12, // NOTIFICATION_TYPES
          actorId: actorId,
          targetId: targetUserId,
          entityType: 1, // ENTITY_TYPES.TASK
          entityId: taskId,
          metadata: {
            taskTitle: task!.title,
            projectTitle: projectInfo!.title,
            freelancerName: freelancer?.name || 'Unknown Freelancer',
            commissionerName: commissioner?.name || 'Unknown Commissioner',
            version: updatedTask.version || 1,
            action: action
          },
          context: {
            projectId: projectId,
            taskId: taskId
          }
        });

        console.log(`✅ Notification logged: ${eventType} for ${eventType === 'task_submitted' ? 'commissioner' : 'freelancer'}`);
        console.log(`📧 Notification details: ${freelancer?.name} → ${commissioner?.name} (Task: "${task.title}")`);
      } catch (eventError) {
        console.error('Failed to log event:', eventError);
        // Don't fail the main operation if event logging fails
      }
    }

    // Auto-generate invoice for completion-based projects when task is approved
    // (Skip if transaction service was used - it handles this)
    if (action === 'complete' && projectInfo?.invoicingMethod === 'completion' && !updatedTask.approvedDate) {
      try {
        const invoiceResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/invoices/auto-generate-completion`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            taskId,
            projectId,
            freelancerId: actualFreelancerId,
            commissionerId: actualCommissionerId,
            taskTitle: task.title,
            projectTitle: projectInfo.title
          })
        });

        if (invoiceResponse.ok) {
          const invoiceResult = await invoiceResponse.json();
          console.log('Auto-generated completion invoice:', invoiceResult);
        }
      } catch (invoiceError) {
        console.error('Failed to auto-generate completion invoice:', invoiceError);
        // Don't fail the main operation if invoice generation fails
      }
    }

    // Auto-complete project if all tasks are approved
    // (Skip if transaction service was used - it handles this)
    if (action === 'complete' && !updatedTask.approvedDate) {
      try {
        const { checkAndAutoCompleteProject } = await import('../../../lib/project-completion/auto-completion-service');
        const completionResult = await checkAndAutoCompleteProject(Number(projectId));

        if (completionResult.statusChanged) {
          console.log(`🎉 Auto-completed project ${projectId}: ${completionResult.message}`);
        } else {
          console.log(`📋 Project ${projectId} not auto-completed: ${completionResult.message}`);
        }
      } catch (completionError) {
        console.error('Failed to auto-complete project:', completionError);
        // Don't fail the main operation if auto-completion fails
      }
    }

    return NextResponse.json(
      ok({
        entities: {
          task: {
            id: task!.id,
            title: task!.title,
            status: updatedTask.status,
            completed: updatedTask.completed,
            version: updatedTask.version,
            projectId: task!.projectId,
          },
        },
        message: `Task ${action} successful`,
        notificationsQueued: !!eventType,
      })
    );
  } catch (error) {
    console.error('Error updating task:', error);
    return NextResponse.json(
      err(ErrorCodes.INTERNAL_ERROR, 'Failed to update task', 500),
      { status: 500 }
    );
  }
}

// Wrap the handler with error handling
export const POST = withErrorHandling(handleTaskSubmission);