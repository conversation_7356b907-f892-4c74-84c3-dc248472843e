import { NextResponse } from 'next/server';
import { getTaskById, updateTask } from '@/app/api/payments/repos/tasks-repo';
import { getProjectById } from '@/app/api/payments/repos/projects-repo';
import { emit as emitBus } from '@/lib/events/bus';
import { TaskService } from '@/app/api/tasks/services/task-service';
import { ok, err, RefreshHints, ErrorCodes, withErrorHandling } from '@/lib/http/envelope';
import { logTaskTransition, Subsystems } from '@/lib/log/transitions';
import { requireSession, assert, assertProjectAccess } from '@/lib/auth/session-guard';
import { parseBody, zTaskApproveBody } from '@/lib/validation/z';

async function handleTaskApproval(request: Request) {
  try {
    // 🔒 Auth - get session and validate
    const { userId: actorId } = await requireSession(request);

    // 🔒 Parse and validate request body
    const body = await parseBody(request, zTaskApproveBody) as { taskId: number; projectId: number };
    const { taskId, projectId } = body;

    // Load project first to validate commissioner access
    const project = await getProjectById(projectId);
    assert(project, ErrorCodes.PROJECT_NOT_FOUND, 404, 'Project not found');

    // 🔒 Ensure session user is the project commissioner
    assertProjectAccess(actorId, project!, 'commissioner');

    // 1) Read task from repo
    const task = await getTaskById(taskId);
    assert(task, ErrorCodes.TASK_NOT_FOUND, 404, 'Task not found');

    // Validate task belongs to project
    assert(Number(task!.projectId) === Number(projectId), ErrorCodes.INVALID_INPUT, 400, 'Task does not belong to provided projectId');

    // 2) Use service for business logic
    let approvalResult;
    try {
      approvalResult = TaskService.approveTask(task!, actorId, 'commissioner');
    } catch (serviceError: any) {
      throw Object.assign(new Error(serviceError.message || 'Cannot approve task'), {
        code: ErrorCodes.INVALID_STATUS_TRANSITION,
        status: 400
      });
    }

    // Check for idempotency (task already approved)
    if (String(task!.status).toLowerCase() === 'approved' || task!.completed === true) {
      return NextResponse.json(
        ok({
          entities: { task: task! },
          refreshHints: [RefreshHints.TASKS_LIST, RefreshHints.PROJECT_TASKS, RefreshHints.DASHBOARD],
          notificationsQueued: false,
          message: 'Task already approved — no changes applied',
        })
      );
    }

    // 3) Persist via repo
    const updated = await updateTask(taskId, approvalResult.taskPatch);
    assert(updated, ErrorCodes.INTERNAL_ERROR, 500, 'Failed to update task');

    const updatedTask = { ...task!, ...approvalResult.taskPatch };

    // 4) Log the transition for observability
    logTaskTransition(
      taskId,
      task!.status,
      'Approved',
      actorId,
      Subsystems.TASKS_APPROVE,
      {
        projectId: Number(projectId),
        taskTitle: task!.title,
      }
    );

    // 5) Emit centralized event via bus (handlers can auto-gen invoices, send notifs, etc.)
    if (approvalResult.shouldNotify) {
      try {
        await emitBus('task.approved', {
          actorId: actorId,
          targetId: Number((task! as any).assigneeId ?? (task! as any).freelancerId ?? 0),
          projectId: Number(projectId),
          taskId: String(taskId),
          taskTitle: task!.title,
        });
      } catch (e) {
        // Do not fail the request if emit fails; log for diagnostics only
        console.warn('[task.approve] bus emit failed:', e);
      }
    }

    return NextResponse.json(
      ok({
        entities: { task: updatedTask },
        refreshHints: [
          RefreshHints.TASKS_LIST,
          RefreshHints.PROJECT_TASKS,
          RefreshHints.INVOICES_LIST,
          RefreshHints.DASHBOARD,
        ],
        notificationsQueued: approvalResult.shouldNotify,
        message: `Task "${task!.title ?? taskId}" approved successfully`,
      })
    );
  } catch (error) {
    console.error('[TASK_APPROVE_ERROR]', error);
    return NextResponse.json(
      err(ErrorCodes.INTERNAL_ERROR, 'Failed to approve task', 500),
      { status: 500 }
    );
  }
}

// Wrap the handler with error handling
export const POST = withErrorHandling(handleTaskApproval);
